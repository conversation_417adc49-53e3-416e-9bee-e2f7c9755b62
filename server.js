const http = require('http');

// Define the port
const PORT = process.env.PORT || 3000;

// Create the server
const server = http.createServer((req, res) => {
  // Set response headers
  res.writeHead(200, {
    'Content-Type': 'text/html',
    'Access-Control-Allow-Origin': '*'
  });

  // Handle different routes
  if (req.url === '/') {
    res.end(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>Web Server</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            h1 { color: #333; }
            .info { background: #f0f0f0; padding: 20px; border-radius: 5px; }
          </style>
        </head>
        <body>
          <h1>Welcome to the Web Server!</h1>
          <div class="info">
            <p>Server is running on port ${PORT}</p>
            <p>Try these routes:</p>
            <ul>
              <li><a href="/api">API endpoint</a></li>
              <li><a href="/health">Health check</a></li>
            </ul>
          </div>
        </body>
      </html>
    `);
  } else if (req.url === '/api') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      message: 'Hello from the API!',
      timestamp: new Date().toISOString(),
      method: req.method
    }));
  } else if (req.url === '/health') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      status: 'OK',
      uptime: process.uptime(),
      timestamp: new Date().toISOString()
    }));
  } else {
    // 404 for unknown routes
    res.writeHead(404, { 'Content-Type': 'text/html' });
    res.end(`
      <!DOCTYPE html>
      <html>
        <head><title>404 Not Found</title></head>
        <body>
          <h1>404 - Page Not Found</h1>
          <p>The requested URL ${req.url} was not found on this server.</p>
          <a href="/">Go back to home</a>
        </body>
      </html>
    `);
  }
});

// Start the server
server.listen(PORT, () => {
  console.log(`Server running at http://localhost:${PORT}/`);
});

// Handle graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  server.close(() => {
    console.log('Process terminated');
  });
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  server.close(() => {
    console.log('Process terminated');
  });
});